// Directorio para esquemas Zod reutilizables
import { z } from 'zod';

export const DocumentoSchema = z.object({
  id: z.string().optional(),
  titulo: z.string().min(1).max(200),
  contenido: z.string().min(1).optional(), // Ahora opcional para el modelo híbrido
  categoria: z.string().optional().nullable(),
  numero_tema: z.union([z.number().int().positive(), z.string(), z.null(), z.undefined()]).optional(),
  creado_en: z.string().optional(),
  actualizado_en: z.string().optional(),
  user_id: z.string().optional(),
  tipo_original: z.string().optional(),
  // Nuevos campos para Storage
  storage_path: z.string().optional(),
  file_size_bytes: z.number().int().positive().optional(),
  content_type: z.string().optional(),
  contenido_corto: z.string().optional(),
});

export const PreguntaSchema = z.object({
  pregunta: z.string().min(1).max(500),
  documentos: z.array(DocumentoSchema).min(1),
});

// Esquemas para el sistema de chunking
const BaseChunkingConfigSchema = z.object({
  maxChunkSize: z.number().int().min(1000).max(100000),
  overlapSize: z.number().int().min(0),
  sectionPatterns: z.array(z.instanceof(RegExp)),
  fallbackToSentences: z.boolean(),
});

export const ChunkingConfigSchema = BaseChunkingConfigSchema.refine(
  (data) => data.overlapSize < data.maxChunkSize,
  {
    message: "overlapSize debe ser menor que maxChunkSize",
    path: ["overlapSize"],
  }
);

export const ChunkMetadataSchema = z.object({
  chunkNumber: z.number().int().positive(),
  totalChunks: z.number().int().positive(),
  sourceDocumentId: z.string().optional(),
  hasOverlap: z.boolean(),
  detectedSections: z.array(z.string()),
  chunkType: z.enum(['section', 'paragraph', 'sentence', 'fixed']),
});

export const ChunkSchema = z.object({
  content: z.string().min(1),
  metadata: ChunkMetadataSchema,
});

export const DocumentProcessingOptionsSchema = z.object({
  enableChunking: z.boolean(),
  chunkingConfig: BaseChunkingConfigSchema.partial().optional(),
  contentType: z.enum(['temario', 'legal', 'tecnico', 'default']).optional(),
  forceChunking: z.boolean().optional(),
  includeStats: z.boolean().optional(),
});

export const GenerarTestSchema = z.object({
  action: z.literal('generarTest'),
  peticion: z.string().min(1).max(500),
  contextos: z.array(z.string().min(1)).optional(),
  documentos: z.array(z.object({
    id: z.string(),
    titulo: z.string(),
    storage_path: z.string().optional(),
    contenido: z.string().optional(),
    contenido_corto: z.string().optional(),
  })).optional(),
  cantidad: z.number().int().min(1).max(50).optional(),
}).refine(data => data.contextos || data.documentos, {
  message: "Se requiere 'contextos' o 'documentos'",
  path: ["contextos", "documentos"]
});

export const GenerarFlashcardsSchema = z.object({
  action: z.literal('generarFlashcards'),
  peticion: z.string().min(1).max(500),
  contextos: z.array(z.string().min(1)).optional(),
  documentos: z.array(z.object({
    id: z.string(),
    titulo: z.string(),
    storage_path: z.string().optional(),
    contenido: z.string().optional(),
    contenido_corto: z.string().optional(),
  })).optional(),
  cantidad: z.number().int().min(1).max(50).optional(),
}).refine(data => data.contextos || data.documentos, {
  message: "Se requiere 'contextos' o 'documentos'",
  path: ["contextos", "documentos"]
});

export const GenerarMapaMentalSchema = z.object({
  action: z.literal('generarMapaMental'),
  peticion: z.string().min(1).max(500),
  contextos: z.array(z.string().min(1)).optional(),
  documentos: z.array(z.object({
    id: z.string(),
    titulo: z.string(),
    storage_path: z.string().optional(),
    contenido: z.string().optional(),
    contenido_corto: z.string().optional(),
  })).optional(),
}).refine(data => data.contextos || data.documentos, {
  message: "Se requiere 'contextos' o 'documentos'",
  path: ["contextos", "documentos"]
});

export const GenerarPlanEstudiosSchema = z.object({
  action: z.literal('generarPlanEstudios'),
  peticion: z.string().min(1), // temarioId viene en peticion para consistencia
  contextos: z.array(z.string()).optional(), // contextos opcionales para mantener interfaz estándar
});

export const GenerarResumenSchema = z.object({
  action: z.literal('generarResumen'),
  peticion: z.string().min(1).max(1000), // titulo|categoria|numero_tema|instrucciones
  contextos: z.array(z.string().min(1)).length(1), // exactamente un documento
});



// Renombrado para reflejar que es un endpoint de AI general, no específicamente Gemini
export const ApiAIInputSchema = z.union([
  PreguntaSchema,
  GenerarTestSchema,
  GenerarFlashcardsSchema,
  GenerarMapaMentalSchema,
  GenerarPlanEstudiosSchema,
  GenerarResumenSchema,
]);

// Mantener compatibilidad con código existente
export const ApiGeminiInputSchema = ApiAIInputSchema;
