// src/lib/supabase/storageService.ts
import { supabase } from './client';

const BUCKET_NAME = 'documentos_usuario';

/**
 * Servicio centralizado para interacciones con Supabase Storage
 * Siguiendo el patrón Service Layer de ARCHITECTURE.md
 */

/**
 * Sube un archivo (Buffer o File) a Supabase Storage.
 * @param path La ruta completa dentro del bucket (incluyendo el nombre del archivo).
 * @param file El contenido del archivo (Buffer, File, etc.).
 * @param contentType El tipo MIME del archivo (ej: 'application/pdf', 'text/plain').
 * @returns La ruta del archivo si la subida es exitosa.
 */
export async function uploadFile(path: string, file: ArrayBuffer | File, contentType: string): Promise<string | null> {
  try {
    console.log('📤 [STORAGE] Iniciando subida de archivo:', {
      path,
      contentType,
      size: file instanceof File ? file.size : file.byteLength,
      timestamp: new Date().toISOString()
    });

    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .upload(path, file, {
        contentType,
        upsert: true, // Sobrescribe si ya existe
      });

    if (error) {
      console.error('❌ [STORAGE] Error al subir archivo:', {
        path,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    console.log('✅ [STORAGE] Archivo subido exitosamente:', {
      path: data.path,
      timestamp: new Date().toISOString()
    });

    return data.path;
  } catch (error) {
    console.error('❌ [STORAGE] Error inesperado al subir archivo:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return null;
  }
}

/**
 * Descarga el contenido de un archivo desde Supabase Storage.
 * @param path La ruta del archivo en el bucket.
 * @returns El contenido del archivo como texto.
 */
export async function downloadFileContent(path: string): Promise<string | null> {
  try {
    console.log('📥 [STORAGE] Iniciando descarga de archivo:', {
      path,
      timestamp: new Date().toISOString()
    });

    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .download(path);

    if (error) {
      console.error('❌ [STORAGE] Error al descargar archivo:', {
        path,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    const content = await data.text();
    
    console.log('✅ [STORAGE] Archivo descargado exitosamente:', {
      path,
      contentLength: content.length,
      timestamp: new Date().toISOString()
    });

    return content;
  } catch (error) {
    console.error('❌ [STORAGE] Error inesperado al descargar archivo:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return null;
  }
}

/**
 * Elimina un archivo de Supabase Storage.
 * @param path La ruta del archivo a eliminar.
 * @returns true si la eliminación fue exitosa.
 */
export async function deleteFile(path: string): Promise<boolean> {
  try {
    console.log('🗑️ [STORAGE] Iniciando eliminación de archivo:', {
      path,
      timestamp: new Date().toISOString()
    });

    const { error } = await supabase.storage
      .from(BUCKET_NAME)
      .remove([path]);

    if (error) {
      console.error('❌ [STORAGE] Error al eliminar archivo:', {
        path,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return false;
    }

    console.log('✅ [STORAGE] Archivo eliminado exitosamente:', {
      path,
      timestamp: new Date().toISOString()
    });

    return true;
  } catch (error) {
    console.error('❌ [STORAGE] Error inesperado al eliminar archivo:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return false;
  }
}

/**
 * Obtiene la URL pública de un archivo (si el bucket fuera público).
 * Nota: Actualmente el bucket es privado, esta función es para uso futuro.
 * @param path La ruta del archivo en el bucket.
 * @returns La URL pública del archivo.
 */
export function getPublicUrl(path: string): string {
  const { data } = supabase.storage
    .from(BUCKET_NAME)
    .getPublicUrl(path);

  return data.publicUrl;
}

/**
 * Genera una URL firmada para acceso temporal a un archivo privado.
 * @param path La ruta del archivo en el bucket.
 * @param expiresIn Tiempo de expiración en segundos (por defecto 1 hora).
 * @returns La URL firmada si es exitosa.
 */
export async function createSignedUrl(path: string, expiresIn: number = 3600): Promise<string | null> {
  try {
    console.log('🔗 [STORAGE] Creando URL firmada:', {
      path,
      expiresIn,
      timestamp: new Date().toISOString()
    });

    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .createSignedUrl(path, expiresIn);

    if (error) {
      console.error('❌ [STORAGE] Error al crear URL firmada:', {
        path,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    console.log('✅ [STORAGE] URL firmada creada exitosamente:', {
      path,
      expiresIn,
      timestamp: new Date().toISOString()
    });

    return data.signedUrl;
  } catch (error) {
    console.error('❌ [STORAGE] Error inesperado al crear URL firmada:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return null;
  }
}

/**
 * Lista archivos en una carpeta específica del bucket.
 * @param folder La carpeta a listar (ej: 'user_id/').
 * @param limit Límite de archivos a retornar.
 * @returns Lista de archivos en la carpeta.
 */
export async function listFiles(folder: string = '', limit: number = 100) {
  try {
    console.log('📋 [STORAGE] Listando archivos:', {
      folder,
      limit,
      timestamp: new Date().toISOString()
    });

    const { data, error } = await supabase.storage
      .from(BUCKET_NAME)
      .list(folder, {
        limit,
        sortBy: { column: 'created_at', order: 'desc' }
      });

    if (error) {
      console.error('❌ [STORAGE] Error al listar archivos:', {
        folder,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    console.log('✅ [STORAGE] Archivos listados exitosamente:', {
      folder,
      count: data.length,
      timestamp: new Date().toISOString()
    });

    return data;
  } catch (error) {
    console.error('❌ [STORAGE] Error inesperado al listar archivos:', {
      folder,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return null;
  }
}
