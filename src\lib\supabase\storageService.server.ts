// src/lib/supabase/storageService.server.ts
// Funciones de Storage específicas para el servidor
import { createServerSupabaseClient } from './server';
import { supabaseAdmin } from './admin';

const BUCKET_NAME = 'documentos_usuario';

/**
 * Sube un archivo desde el servidor usando el contexto de autenticación del servidor
 */
export async function uploadFileFromServer(path: string, file: ArrayBuffer | File, contentType: string): Promise<string | null> {
  try {
    console.log('📤 [STORAGE_SERVER] Iniciando subida de archivo desde servidor:', {
      path,
      contentType,
      size: file instanceof File ? file.size : file.byteLength,
      timestamp: new Date().toISOString()
    });

    // Usar cliente de servidor con contexto de autenticación
    const serverSupabase = await createServerSupabaseClient();
    
    // Verificar autenticación del servidor
    const { data: { user }, error: authError } = await serverSupabase.auth.getUser();
    console.log('🔐 [STORAGE_SERVER] Contexto de autenticación del servidor:', {
      hasUser: !!user,
      userId: user?.id || 'null',
      authError: authError?.message || 'none',
      timestamp: new Date().toISOString()
    });

    // Determinar qué cliente usar
    let clientToUse = serverSupabase;
    let useAdminClient = false;

    // Si no hay usuario autenticado, usar cliente admin
    if (!user || authError) {
      console.log('🔧 [STORAGE_SERVER] Usando cliente de administrador debido a falta de autenticación');
      console.log('🔧 [STORAGE_SERVER] Detalles del error de autenticación:', {
        hasUser: !!user,
        authError: authError?.message,
        authErrorCode: authError?.status
      });
      clientToUse = supabaseAdmin;
      useAdminClient = true;
    }

    const { data, error } = await clientToUse.storage
      .from(BUCKET_NAME)
      .upload(path, file, {
        contentType,
        upsert: true,
      });

    if (error) {
      console.error('❌ [STORAGE_SERVER] Error al subir archivo:', {
        path,
        error: error.message,
        errorDetails: error,
        usedAdminClient: useAdminClient,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    console.log('✅ [STORAGE_SERVER] Archivo subido exitosamente:', {
      path: data.path,
      usedAdminClient: useAdminClient,
      timestamp: new Date().toISOString()
    });

    return data.path;
  } catch (error) {
    console.error('❌ [STORAGE_SERVER] Error inesperado al subir archivo:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return null;
  }
}

/**
 * Descarga un archivo desde el servidor
 */
export async function downloadFileFromServer(path: string): Promise<ArrayBuffer | null> {
  try {
    console.log('📥 [STORAGE_SERVER] Iniciando descarga de archivo desde servidor:', {
      path,
      timestamp: new Date().toISOString()
    });

    const serverSupabase = await createServerSupabaseClient();
    const { data, error } = await serverSupabase.storage
      .from(BUCKET_NAME)
      .download(path);

    if (error) {
      console.error('❌ [STORAGE_SERVER] Error al descargar archivo:', {
        path,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return null;
    }

    console.log('✅ [STORAGE_SERVER] Archivo descargado exitosamente:', {
      path,
      size: data.size,
      timestamp: new Date().toISOString()
    });

    return await data.arrayBuffer();
  } catch (error) {
    console.error('❌ [STORAGE_SERVER] Error inesperado al descargar archivo:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return null;
  }
}

/**
 * Elimina un archivo desde el servidor
 */
export async function deleteFileFromServer(path: string): Promise<boolean> {
  try {
    console.log('🗑️ [STORAGE_SERVER] Iniciando eliminación de archivo desde servidor:', {
      path,
      timestamp: new Date().toISOString()
    });

    const serverSupabase = await createServerSupabaseClient();
    const { error } = await serverSupabase.storage
      .from(BUCKET_NAME)
      .remove([path]);

    if (error) {
      console.error('❌ [STORAGE_SERVER] Error al eliminar archivo:', {
        path,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      return false;
    }

    console.log('✅ [STORAGE_SERVER] Archivo eliminado exitosamente:', {
      path,
      timestamp: new Date().toISOString()
    });

    return true;
  } catch (error) {
    console.error('❌ [STORAGE_SERVER] Error inesperado al eliminar archivo:', {
      path,
      error: error instanceof Error ? error.message : 'Error desconocido',
      timestamp: new Date().toISOString()
    });
    return false;
  }
}
