import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Documento } from '../../../lib/supabase';
import { mindMapFormSchema } from '../../../lib/formSchemas';
import { useBackgroundGeneration } from '@/hooks/useBackgroundGeneration';
import { useBackgroundTasks } from '@/contexts/BackgroundTasksContext';
import { useTaskResults } from '@/hooks/useTaskResults';
import MindMapHelp from './MindMapHelp';
import { toast } from 'react-hot-toast';
import { useFreeAccountGuard } from '@/components/ui/FreeAccountGuard';
import { useAuth } from '@/contexts/AuthContext';
import { useUserPlan } from '@/hooks/useUserPlan';
import { usePlanLimits } from '@/hooks/usePlanLimits';
import { PLAN_CONFIGURATIONS } from '@/config/plans';
import { FiLock, FiArrowUp } from 'react-icons/fi';
import Link from 'next/link';
import { MindMapRecommendationsModal } from '@/components/ui/RecommendationsModal';

interface MindMapGeneratorProps {
  documentosSeleccionados: Documento[];
}

export default function MindMapGenerator({ documentosSeleccionados }: MindMapGeneratorProps) {
  const [mapaGenerado, setMapaGenerado] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showBackgroundOption, setShowBackgroundOption] = useState(false);
  const [showRecommendations, setShowRecommendations] = useState(false);

  const { generateMapaMental, isGenerating, getActiveTask } = useBackgroundGeneration();
  const { getTask } = useBackgroundTasks();
  const { executeWithGuard } = useFreeAccountGuard();
  const { user } = useAuth();
  const { plan: userPlan, isLoading: planLoading } = useUserPlan();
  const { refresh: refreshPlanLimits } = usePlanLimits();

  // Verificar si hay una tarea activa de mapa mental
  const activeTask = getActiveTask('mapa-mental');
  const isLoading = isGenerating('mapa-mental');

  // Suscribirse a los resultados de las tareas de mapas mentales
  const { resetProcessed, getLatestCompletedTask, isInitialized } = useTaskResults({
    taskType: 'mapa-mental',
    onResult: (result) => {
      setMapaGenerado(result);
      toast.success('¡Mapa mental generado exitosamente!');
      refreshPlanLimits(); // Refrescar límites después de generación exitosa
    },
    onError: (error) => {
      toast.error(`Error al generar mapa mental: ${error}`);
    }
  });

  // Verificar si hay resultados de mapas mentales al montar el componente
  useEffect(() => {
    if (!isInitialized) return;

    // Solo verificar si no hay mapa generado actualmente
    if (!mapaGenerado) {
      const latestTask = getLatestCompletedTask();
      if (latestTask && latestTask.result) {
        console.log('🔄 Aplicando resultado de mapa mental recuperado automáticamente');
        setMapaGenerado(latestTask.result);

        toast.success('Mapa mental recuperado automáticamente', {
          duration: 3000,
          icon: '🔄'
        });
      }
    }
  }, [isInitialized, mapaGenerado, getLatestCompletedTask]);

  const {
    register,
    handleSubmit: handleSubmitForm,
    formState: { errors }
  } = useForm({
    resolver: zodResolver(mindMapFormSchema),
    defaultValues: { peticion: '' }
  });

  // Manejar tecla Escape para cerrar pantalla completa
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isFullscreen) {
        setIsFullscreen(false);
      }
    };

    if (isFullscreen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden'; // Prevenir scroll del body
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isFullscreen]);

  const onSubmit = async (data: { peticion: string }) => {
    const contextos = documentosSeleccionados.map(doc => doc.contenido || doc.contenido_corto || '').filter(Boolean);

    console.log('🗺️ Iniciando generación de mapa mental:', {
      peticion: data.peticion,
      documentos: documentosSeleccionados.length,
      contextosLength: contextos.length
    });

    // Validar límites de plan gratuito
    const result = await executeWithGuard('mindMaps', async () => {
      const taskId = await generateMapaMental({
        peticion: data.peticion,
        contextos
      });

      console.log('✅ Tarea de mapa mental creada:', taskId);
      return taskId;
    }, 1);

    if (result.success) {
      // Mostrar mensaje informativo sobre la generación en segundo plano
      toast.success('Generación iniciada en segundo plano. Puedes continuar usando la aplicación.', {
        duration: 4000,
      });
    } else {
      console.error('❌ Error al generar mapa mental:', result.error);
      toast.error(result.error || 'Error al iniciar la generación del mapa mental');
    }
  };

  const handleDownload = () => {
    if (!mapaGenerado) return;

    // Crear un blob con el contenido HTML
    const blob = new Blob([mapaGenerado], { type: 'text/html' });

    // Crear un enlace para descargar el archivo
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'mapa-mental.html';
    document.body.appendChild(a);
    a.click();

    // Limpiar
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 0);
  };

  const handleFullscreen = () => {
    setIsFullscreen(true);
  };

  const handleCloseFullscreen = () => {
    setIsFullscreen(false);
  };

  return (
    <div className="mt-8 border-t pt-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">Generador de Mapas Mentales</h2>
        <MindMapHelp />
      </div>

      {/* Información de límites según el plan del usuario */}
      {!planLoading && userPlan === 'free' && (
        <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
          <div className="flex items-start space-x-3">
            <FiLock className="w-5 h-5 text-purple-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-purple-900">Límites del Plan Gratuito</h3>
              <p className="text-sm text-purple-700 mt-1">
                Máximo {PLAN_CONFIGURATIONS.free.limits.mindMapsForTrial} mapas mentales durante el período de prueba. Para generar mapas mentales ilimitados,
                <Link href="/upgrade-plan" className="font-medium underline hover:text-purple-800 ml-1">
                  actualiza tu plan
                </Link>.
              </p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmitForm(onSubmit)} className="space-y-4">
        <div>
          <div className="flex items-center justify-between mb-2">
            <label htmlFor="peticion" className="text-gray-700 text-sm font-bold">
              Describe el mapa mental que deseas generar:
            </label>
            <button
              type="button"
              onClick={() => setShowRecommendations(true)}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium underline"
            >
              💡 Ver recomendaciones
            </button>
          </div>
          <input
            id="peticion"
            type="text"
            {...register('peticion')}
            disabled={isLoading}
            className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            placeholder="Ej: Genera un mapa mental sobre los conceptos principales del tema 1"
          />
          {errors.peticion && <span className="text-red-500 text-sm">{errors.peticion.message}</span>}
          <p className="text-sm text-gray-500 mt-1">
            La IA generará un mapa mental basado en los documentos seleccionados y tu petición.
          </p>
        </div>

        <div className="flex items-center justify-between">
          <button
            type="submit"
            className="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50"
            disabled={isLoading || documentosSeleccionados.length === 0}
          >
            {isLoading ? 'Generando en segundo plano...' : 'Generar Mapa Mental'}
          </button>

          {activeTask && (
            <div className="text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-lg">
              <span className="font-medium">Generando:</span> {activeTask.title}
            </div>
          )}
        </div>
      </form>

      {isLoading && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <div>
              <p className="text-blue-800 font-medium">Generando mapa mental en segundo plano</p>
              <p className="text-blue-600 text-sm">Puedes continuar usando otras funciones de la aplicación</p>
            </div>
          </div>
        </div>
      )}

      {mapaGenerado && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Vista previa:</h3>
          <div className="bg-gray-100 p-4 rounded-lg border overflow-hidden" style={{ maxHeight: '500px' }}>
            <iframe
              srcDoc={mapaGenerado}
              title="Vista previa del mapa mental"
              className="w-full h-96 border-0"
              sandbox="allow-scripts allow-same-origin"
            />
          </div>
          <div className="flex justify-between items-center mt-2">
            <p className="text-sm text-gray-500">
              Vista previa limitada. Usa pantalla completa o descarga para mejor experiencia.
            </p>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={handleFullscreen}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Pantalla Completa
              </button>
              <button
                type="button"
                onClick={handleDownload}
                className="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                Descargar Mapa Mental
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Pantalla Completa */}
      {isFullscreen && mapaGenerado && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center animate-fadeIn">
          <div className="relative w-full h-full max-w-none max-h-none p-4 animate-scaleIn">
            {/* Barra de herramientas superior */}
            <div className="absolute top-4 left-4 right-4 z-10 flex justify-between items-center bg-white bg-opacity-90 backdrop-blur-sm rounded-lg px-4 py-2 shadow-lg">
              <div className="flex items-center space-x-4">
                <h3 className="text-lg font-semibold text-gray-800">Mapa Mental - Vista Completa</h3>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleDownload}
                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm flex items-center"
                  title="Descargar mapa mental"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  Descargar
                </button>
                <button
                  onClick={handleCloseFullscreen}
                  className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm flex items-center"
                  title="Cerrar pantalla completa"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  Cerrar
                </button>
              </div>
            </div>

            {/* Contenedor del mapa mental */}
            <div className="w-full h-full pt-16 pb-4">
              <iframe
                srcDoc={mapaGenerado}
                title="Mapa mental en pantalla completa"
                className="w-full h-full border-0 rounded-lg shadow-2xl bg-white"
                sandbox="allow-scripts allow-same-origin"
              />
            </div>
          </div>

          {/* Overlay para cerrar al hacer clic fuera */}
          <div
            className="absolute inset-0 -z-10"
            onClick={handleCloseFullscreen}
            aria-label="Cerrar pantalla completa"
          />
        </div>
      )}

      {/* Modal de recomendaciones */}
      <MindMapRecommendationsModal
        isOpen={showRecommendations}
        onClose={() => setShowRecommendations(false)}
      />
    </div>
  );
}
